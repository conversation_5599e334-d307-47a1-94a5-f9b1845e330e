import * as CANNON from 'cannon-es';

/**
 * Physics world management using Cannon.js
 * Handles physics simulation, collision detection, and world setup
 */
export class PhysicsWorld {
    constructor(config) {
        this.config = config;
        this.world = null;
        this.materials = {};
        this.contactMaterials = {};
        this.bodies = new Map();
        
        // Debug
        this.debugRenderer = null;
    }

    /**
     * Initialize the physics world
     */
    init() {
        // Create physics world
        this.world = new CANNON.World({
            gravity: new CANNON.Vec3(0, this.config.gravity, 0),
            broadphase: this.createBroadphase(),
            solver: this.createSolver()
        });

        // Set up materials
        this.setupMaterials();
        
        // Set up collision groups
        this.setupCollisionGroups();
        
        console.log('⚡ Physics world initialized');
    }

    /**
     * Create broadphase collision detection
     */
    createBroadphase() {
        switch (this.config.broadphase) {
            case 'sap':
                return new CANNON.SAPBroadphase(this.world);
            case 'naive':
            default:
                return new CANNON.NaiveBroadphase();
        }
    }

    /**
     * Create constraint solver
     */
    createSolver() {
        const solver = new CANNON.GSSolver();
        solver.iterations = this.config.solver.iterations;
        solver.tolerance = this.config.solver.tolerance;
        return solver;
    }

    /**
     * Set up physics materials
     */
    setupMaterials() {
        // Car material
        this.materials.car = new CANNON.Material('car');
        this.materials.car.friction = 0.4;
        this.materials.car.restitution = 0.1;
        
        // Track material
        this.materials.track = new CANNON.Material('track');
        this.materials.track.friction = 0.8;
        this.materials.track.restitution = 0.2;
        
        // Ground material (fallback)
        this.materials.ground = new CANNON.Material('ground');
        this.materials.ground.friction = 0.6;
        this.materials.ground.restitution = 0.1;
        
        // Barrier material
        this.materials.barrier = new CANNON.Material('barrier');
        this.materials.barrier.friction = 0.3;
        this.materials.barrier.restitution = 0.8;
        
        // Set up contact materials (interactions between materials)
        this.setupContactMaterials();
    }

    /**
     * Set up contact materials for material interactions
     */
    setupContactMaterials() {
        // Car-Track interaction
        this.contactMaterials.carTrack = new CANNON.ContactMaterial(
            this.materials.car,
            this.materials.track,
            {
                friction: 0.2,  // Further reduced friction for better movement
                restitution: 0.1,
                contactEquationStiffness: 1e6,  // Reduced stiffness to prevent sticking
                contactEquationRelaxation: 4,
                frictionEquationStiffness: 1e6,  // Reduced stiffness
                frictionEquationRelaxation: 4
            }
        );
        this.world.addContactMaterial(this.contactMaterials.carTrack);

        // Car-Ground interaction
        this.contactMaterials.carGround = new CANNON.ContactMaterial(
            this.materials.car,
            this.materials.ground,
            {
                friction: 0.3,  // Significantly reduced friction
                restitution: 0.1,  // Reduced bounce
                contactEquationStiffness: 1e6,  // Reduced stiffness to prevent sticking
                contactEquationRelaxation: 4
            }
        );
        this.world.addContactMaterial(this.contactMaterials.carGround);
        
        // Car-Barrier interaction
        this.contactMaterials.carBarrier = new CANNON.ContactMaterial(
            this.materials.car,
            this.materials.barrier,
            {
                friction: 0.3,
                restitution: 0.6,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3
            }
        );
        this.world.addContactMaterial(this.contactMaterials.carBarrier);
    }

    /**
     * Set up collision groups for efficient collision detection
     */
    setupCollisionGroups() {
        // Define collision groups
        this.collisionGroups = {
            CAR: 1,
            TRACK: 2,
            BARRIER: 4,
            GROUND: 8,
            CHECKPOINT: 16
        };
    }

    /**
     * Step the physics simulation
     */
    step(deltaTime) {
        this.world.step(
            this.config.timeStep,
            deltaTime,
            this.config.maxSubSteps
        );
        
        // Update all registered bodies
        this.updateBodies();
    }

    /**
     * Update all registered physics bodies
     */
    updateBodies() {
        for (const [id, bodyData] of this.bodies) {
            if (bodyData.mesh && bodyData.body) {
                // Update mesh position and rotation from physics body
                bodyData.mesh.position.copy(bodyData.body.position);
                bodyData.mesh.quaternion.copy(bodyData.body.quaternion);
            }
        }
    }

    /**
     * Add a physics body to the world
     */
    addBody(body, mesh = null, id = null) {
        this.world.addBody(body);
        
        if (id && mesh) {
            this.bodies.set(id, { body, mesh });
        }
        
        return body;
    }

    /**
     * Remove a physics body from the world
     */
    removeBody(body, id = null) {
        this.world.removeBody(body);
        
        if (id) {
            this.bodies.delete(id);
        }
    }

    /**
     * Create a box physics body
     */
    createBox(size, mass = 0, material = null) {
        const shape = new CANNON.Box(new CANNON.Vec3(size.x / 2, size.y / 2, size.z / 2));
        const body = new CANNON.Body({ mass, material });
        body.addShape(shape);
        return body;
    }

    /**
     * Create a sphere physics body
     */
    createSphere(radius, mass = 0, material = null) {
        const shape = new CANNON.Sphere(radius);
        const body = new CANNON.Body({ mass, material });
        body.addShape(shape);
        return body;
    }

    /**
     * Create a plane physics body
     */
    createPlane(material = null) {
        const shape = new CANNON.Plane();
        const body = new CANNON.Body({ mass: 0, material });
        body.addShape(shape);
        return body;
    }

    /**
     * Create a cylinder physics body
     */
    createCylinder(radiusTop, radiusBottom, height, numSegments = 8, mass = 0, material = null) {
        const shape = new CANNON.Cylinder(radiusTop, radiusBottom, height, numSegments);
        const body = new CANNON.Body({ mass, material });
        body.addShape(shape);
        return body;
    }

    /**
     * Create a plane physics body
     */
    createPlane(material = null) {
        const shape = new CANNON.Plane();
        const body = new CANNON.Body({ mass: 0, material });
        body.addShape(shape);
        return body;
    }

    /**
     * Create a heightfield from terrain data
     */
    createHeightfield(data, options = {}) {
        const shape = new CANNON.Heightfield(data, {
            elementSize: options.elementSize || 1,
            ...options
        });
        const body = new CANNON.Body({ mass: 0, material: options.material });
        body.addShape(shape);
        return body;
    }

    /**
     * Create a trimesh from geometry
     */
    createTrimesh(vertices, indices, material = null) {
        const shape = new CANNON.Trimesh(vertices, indices);
        const body = new CANNON.Body({ mass: 0, material });
        body.addShape(shape);
        return body;
    }

    /**
     * Raycast from a point in a direction
     */
    raycast(from, to, options = {}) {
        const raycastResult = new CANNON.RaycastResult();
        const ray = new CANNON.Ray(from, to);
        
        this.world.raycastClosest(from, to, options, raycastResult);
        
        return {
            hasHit: raycastResult.hasHit,
            body: raycastResult.body,
            point: raycastResult.hitPointWorld,
            normal: raycastResult.hitNormalWorld,
            distance: raycastResult.distance
        };
    }

    /**
     * Get material by name
     */
    getMaterial(name) {
        return this.materials[name];
    }

    /**
     * Get collision group by name
     */
    getCollisionGroup(name) {
        return this.collisionGroups[name];
    }

    /**
     * Set debug mode
     */
    setDebugMode(enabled) {
        // This would integrate with a debug renderer
        // For now, just log the state
        console.log(`Physics debug mode: ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Get world statistics
     */
    getStats() {
        return {
            bodies: this.world.bodies.length,
            contacts: this.world.contacts.length,
            materials: Object.keys(this.materials).length,
            contactMaterials: Object.keys(this.contactMaterials).length
        };
    }

    /**
     * Cleanup physics world
     */
    destroy() {
        if (this.world) {
            // Remove all bodies
            while (this.world.bodies.length > 0) {
                this.world.removeBody(this.world.bodies[0]);
            }
            
            // Clear collections
            this.bodies.clear();
            this.materials = {};
            this.contactMaterials = {};
            
            this.world = null;
        }
    }
}
