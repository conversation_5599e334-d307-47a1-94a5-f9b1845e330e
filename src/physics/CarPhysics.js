import * as CANNON from 'cannon-es';
import * as THREE from 'three';

/**
 * Advanced car physics system with highly configurable parameters
 * Implements realistic but arcade-style physics for fast-paced racing
 */
export class CarPhysics {
    constructor(config, physicsWorld) {
        this.config = config;
        this.world = physicsWorld;
        
        // Physics body
        this.body = null;
        this.wheels = [];
        
        // Current state
        this.state = {
            speed: 0,           // Current speed in km/h
            rpm: 1000,          // Engine RPM
            gear: 1,            // Current gear
            boost: 100,         // Boost percentage
            isGrounded: false,  // Is car touching ground
            groundNormal: new THREE.Vector3(0, 1, 0),
            
            // Input state
            throttle: 0,        // -1 to 1 (reverse to forward)
            steering: 0,        // -1 to 1 (left to right)
            brake: 0,           // 0 to 1
            handbrake: false,
            boostActive: false
        };
        
        // Forces and torques
        this.forces = {
            engine: new CANNON.Vec3(),
            brake: new CANNON.Vec3(),
            steering: new CANNON.Vec3(),
            downforce: new CANNON.Vec3(),
            drag: new CANNON.Vec3(),
            hover: new CANNON.Vec3()
        };
        
        // Raycast for ground detection
        this.groundRays = [];
        this.setupGroundRays();
    }

    /**
     * Initialize car physics body
     */
    init() {
        this.createCarBody();
        this.setupSuspension();
        return this.body;
    }

    /**
     * Create the main car physics body
     */
    createCarBody() {
        const dims = this.config.physics.dimensions;
        const mass = this.config.physics.mass;
        
        // Create car body shape
        const carShape = new CANNON.Box(new CANNON.Vec3(dims.width/2, dims.height/2, dims.length/2));
        
        // Create physics body
        this.body = new CANNON.Body({ 
            mass: mass,
            material: this.world.getMaterial('car')
        });
        
        this.body.addShape(carShape);
        
        // Set center of mass
        const com = this.config.physics.centerOfMass;
        this.body.shapeOffsets[0].set(com.x, com.y, com.z);
        
        // Set inertia tensor for realistic rotation
        const inertia = this.config.physics.inertia;
        this.body.inertia.set(inertia.x, inertia.y, inertia.z);
        this.body.invInertia.set(1/inertia.x, 1/inertia.y, 1/inertia.z);

        // Set collision groups
        this.body.collisionFilterGroup = this.world.getCollisionGroup('CAR');
        this.body.collisionFilterMask = this.world.getCollisionGroup('TRACK') | this.world.getCollisionGroup('GROUND') | this.world.getCollisionGroup('BARRIER');

        // Add to physics world
        this.world.addBody(this.body, null, 'car');
    }

    /**
     * Set up suspension system for hover effect
     */
    setupSuspension() {
        // Create suspension points (wheels)
        const dims = this.config.physics.dimensions;
        const suspensionConfig = this.config.suspension;
        
        this.wheels = [
            // Front left
            { position: new CANNON.Vec3(-dims.width/2, -dims.height/2, dims.length/3) },
            // Front right  
            { position: new CANNON.Vec3(dims.width/2, -dims.height/2, dims.length/3) },
            // Rear left
            { position: new CANNON.Vec3(-dims.width/2, -dims.height/2, -dims.length/3) },
            // Rear right
            { position: new CANNON.Vec3(dims.width/2, -dims.height/2, -dims.length/3) }
        ];
    }

    /**
     * Set up ground detection rays
     */
    setupGroundRays() {
        const dims = this.config.physics.dimensions;
        
        // Create raycast points for ground detection
        this.groundRays = [
            { offset: new CANNON.Vec3(-dims.width/3, 0, dims.length/3) },   // Front left
            { offset: new CANNON.Vec3(dims.width/3, 0, dims.length/3) },    // Front right
            { offset: new CANNON.Vec3(-dims.width/3, 0, -dims.length/3) },  // Rear left
            { offset: new CANNON.Vec3(dims.width/3, 0, -dims.length/3) },   // Rear right
            { offset: new CANNON.Vec3(0, 0, 0) }                            // Center
        ];
    }

    /**
     * Update car physics
     */
    update(deltaTime, inputState) {
        // Update input state
        this.updateInputState(inputState);
        
        // Perform ground detection
        this.updateGroundDetection();
        
        // Calculate and apply forces
        this.calculateEngineForce(deltaTime);
        this.calculateBrakingForce(deltaTime);
        this.calculateSteeringForce(deltaTime);
        this.calculateAerodynamicForces(deltaTime);
        this.calculateSuspensionForces(deltaTime);
        
        // Apply all forces
        this.applyForces();
        
        // Update state
        this.updateState(deltaTime);
        
        // Apply stability systems
        this.applyStabilitySystems(deltaTime);
    }

    /**
     * Update input state from input system
     */
    updateInputState(inputState) {
        this.state.throttle = inputState.throttle || 0;
        this.state.steering = inputState.steering || 0;
        this.state.brake = inputState.brake || 0;
        this.state.handbrake = inputState.handbrake || false;
        this.state.boostActive = inputState.boost || false;
    }

    /**
     * Perform ground detection using raycasting
     */
    updateGroundDetection() {
        let groundHits = 0;
        let averageNormal = new CANNON.Vec3();
        const maxDistance = this.config.suspension.maxHoverDistance;
        
        this.state.isGrounded = false;
        
        for (const ray of this.groundRays) {
            const worldPos = new CANNON.Vec3();
            this.body.pointToWorldFrame(ray.offset, worldPos);
            
            const rayStart = worldPos.clone();
            const rayEnd = worldPos.clone();
            rayEnd.y -= maxDistance;
            
            const result = this.world.raycast(rayStart, rayEnd, {
                collisionFilterGroup: this.world.getCollisionGroup('CAR'),
                collisionFilterMask: this.world.getCollisionGroup('TRACK') | this.world.getCollisionGroup('GROUND')
            });
            
            if (result.hasHit) {
                groundHits++;
                averageNormal.vadd(result.normal, averageNormal);
                this.state.isGrounded = true;
            }
        }
        
        if (groundHits > 0) {
            averageNormal.scale(1 / groundHits, averageNormal);
            this.state.groundNormal.set(averageNormal.x, averageNormal.y, averageNormal.z);
        }
    }

    /**
     * Calculate engine force
     */
    calculateEngineForce(deltaTime) {
        const engineConfig = this.config.engine;
        let force = 0;

        if (Math.abs(this.state.throttle) > 0.01) {
            // Calculate base engine force
            force = this.state.throttle * engineConfig.acceleration.force;

            // Apply boost if active
            if (this.state.boostActive && this.state.boost > 0) {
                force *= engineConfig.boost.multiplier;
                this.state.boost = Math.max(0, this.state.boost - engineConfig.boost.consumption * deltaTime * 100);
            }

            // Apply efficiency
            force *= engineConfig.acceleration.efficiency;

            // Increase force when grounded for better traction
            if (this.state.isGrounded) {
                force *= 1.2;
            }
        }

        // Convert to world space force in forward direction
        // Use Z as forward direction (standard Three.js convention)
        const forwardDirection = new CANNON.Vec3(0, 0, 1);
        this.body.vectorToWorldFrame(forwardDirection, forwardDirection);

        this.forces.engine.copy(forwardDirection);
        this.forces.engine.scale(force, this.forces.engine);
    }

    /**
     * Calculate braking force
     */
    calculateBrakingForce(deltaTime) {
        const brakeConfig = this.config.engine.braking;
        let brakeForce = 0;
        
        if (this.state.brake > 0.01 || this.state.handbrake) {
            const brakeInput = Math.max(this.state.brake, this.state.handbrake ? 1.0 : 0);
            brakeForce = brakeInput * brakeConfig.force * brakeConfig.efficiency;
        }
        
        // Apply brake force opposite to velocity
        const velocity = this.body.velocity.clone();
        velocity.normalize();
        velocity.scale(-brakeForce, velocity);
        
        this.forces.brake.copy(velocity);
    }

    /**
     * Calculate steering force
     */
    calculateSteeringForce(deltaTime) {
        if (Math.abs(this.state.steering) < 0.01 || !this.state.isGrounded) {
            this.forces.steering.set(0, 0, 0);
            return;
        }
        
        const steeringConfig = this.config.steering;
        const speed = this.getSpeedKmh();
        
        // Calculate speed-sensitive steering
        let steeringSensitivity = steeringConfig.sensitivity;
        if (steeringConfig.speedSensitivity.enabled) {
            const speedFactor = THREE.MathUtils.clamp(
                (speed - steeringConfig.speedSensitivity.minSpeed) / 
                (steeringConfig.speedSensitivity.maxSpeed - steeringConfig.speedSensitivity.minSpeed),
                0, 1
            );
            steeringSensitivity *= THREE.MathUtils.lerp(1, steeringConfig.speedSensitivity.minMultiplier, speedFactor);
        }
        
        // Calculate steering angle
        const steeringAngle = this.state.steering * steeringConfig.maxAngle * steeringSensitivity * (Math.PI / 180);
        
        // Calculate lateral force based on tire grip
        const lateralForce = Math.sin(steeringAngle) * this.config.tires.grip.lateral * speed * 100;
        
        // Apply force in local right direction
        const rightDirection = new CANNON.Vec3(1, 0, 0);
        this.body.vectorToWorldFrame(rightDirection, rightDirection);
        
        this.forces.steering.copy(rightDirection);
        this.forces.steering.scale(lateralForce, this.forces.steering);
    }

    /**
     * Calculate aerodynamic forces (drag and downforce)
     */
    calculateAerodynamicForces(deltaTime) {
        const aeroConfig = this.config.aerodynamics;
        const velocity = this.body.velocity;
        const speed = velocity.length();
        
        if (speed < 1) {
            this.forces.drag.set(0, 0, 0);
            this.forces.downforce.set(0, 0, 0);
            return;
        }
        
        // Calculate drag force
        const dragMagnitude = 0.5 * aeroConfig.airDensity * aeroConfig.dragCoefficient * 
                             aeroConfig.frontalArea * speed * speed;
        
        const dragDirection = velocity.clone();
        dragDirection.normalize();
        dragDirection.scale(-dragMagnitude, dragDirection);
        this.forces.drag.copy(dragDirection);
        
        // Calculate downforce
        const downforceMagnitude = 0.5 * aeroConfig.airDensity * aeroConfig.downforce.coefficient * 
                                  aeroConfig.frontalArea * speed * speed;
        
        this.forces.downforce.set(0, -downforceMagnitude, 0);
    }

    /**
     * Calculate suspension forces for hover effect
     */
    calculateSuspensionForces(deltaTime) {
        const suspensionConfig = this.config.suspension;

        // Reset hover force
        this.forces.hover.set(0, 0, 0);

        // Apply hover forces when grounded to maintain target height
        if (this.state.isGrounded) {
            // Calculate hover force to maintain target height
            const targetHeight = suspensionConfig.hoverHeight;
            const currentHeight = this.body.position.y;
            const heightDifference = targetHeight - currentHeight;

            const hoverForce = heightDifference * suspensionConfig.hoverForce;
            const dampingForce = -this.body.velocity.y * suspensionConfig.hoverDamping;

            this.forces.hover.set(0, hoverForce + dampingForce, 0);
        } else {
            // Apply slight upward force when not grounded to prevent sticking
            const antiGravityForce = Math.abs(this.config.physics.mass * this.world.world.gravity.y) * 0.1;
            this.forces.hover.set(0, antiGravityForce, 0);
        }
    }

    /**
     * Apply all calculated forces to the car body
     */
    applyForces() {
        // Apply forces at center of mass
        this.body.force.vadd(this.forces.engine, this.body.force);
        this.body.force.vadd(this.forces.brake, this.body.force);
        this.body.force.vadd(this.forces.steering, this.body.force);
        this.body.force.vadd(this.forces.drag, this.body.force);
        this.body.force.vadd(this.forces.downforce, this.body.force);
        this.body.force.vadd(this.forces.hover, this.body.force);

        // Debug: Log forces when throttle is applied (uncomment for debugging)
        // if (Math.abs(this.state.throttle) > 0.01) {
        //     console.log('Forces:', {
        //         engine: this.forces.engine.length(),
        //         hover: this.forces.hover.length(),
        //         isGrounded: this.state.isGrounded,
        //         throttle: this.state.throttle,
        //         velocity: this.body.velocity.length()
        //     });
        // }
    }

    /**
     * Update car state
     */
    updateState(deltaTime) {
        // Update speed
        this.state.speed = this.getSpeedKmh();
        
        // Update boost regeneration
        if (!this.state.boostActive) {
            this.state.boost = Math.min(100, this.state.boost + this.config.engine.boost.consumption * deltaTime * 50);
        }
        
        // Update RPM based on speed (simplified)
        const maxSpeed = 300; // km/h
        this.state.rpm = 1000 + (this.state.speed / maxSpeed) * 7000;
    }

    /**
     * Apply stability systems (traction control, stability control, ABS)
     */
    applyStabilitySystems(deltaTime) {
        const stabilityConfig = this.config.stability;
        
        // Simplified stability systems
        if (stabilityConfig.stabilityControl.enabled) {
            // Apply stability correction if car is sliding
            const angularVelocity = this.body.angularVelocity;
            if (angularVelocity.length() > stabilityConfig.stabilityControl.threshold) {
                const correction = angularVelocity.clone();
                correction.scale(-stabilityConfig.stabilityControl.correction, correction);
                this.body.angularVelocity.vadd(correction, this.body.angularVelocity);
            }
        }
    }

    /**
     * Get current speed in km/h
     */
    getSpeedKmh() {
        return this.body.velocity.length() * 3.6; // m/s to km/h
    }

    /**
     * Get current speed in m/s
     */
    getSpeedMs() {
        return this.body.velocity.length();
    }

    /**
     * Get car state for external systems
     */
    getState() {
        return { ...this.state };
    }

    /**
     * Reset car physics
     */
    reset(position, rotation) {
        this.body.position.copy(position);
        this.body.quaternion.copy(rotation);
        this.body.velocity.set(0, 0, 0);
        this.body.angularVelocity.set(0, 0, 0);
        this.body.force.set(0, 0, 0);
        this.body.torque.set(0, 0, 0);
        
        // Reset state
        this.state.speed = 0;
        this.state.rpm = 1000;
        this.state.boost = 100;
    }
}
